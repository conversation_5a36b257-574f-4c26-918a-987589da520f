<?php $__env->startSection('content'); ?>
    <h3>Send Message</h3>

    <form id="messageForm">
        <?php echo csrf_field(); ?>
        <div class="mb-3">
            <input type="text" class="form-control" name="message" id="messageInput" placeholder="Enter message" required>
        </div>
        <button type="submit" class="btn btn-primary">Send</button>
    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $('#messageForm').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: '/send-message',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    message: $('#messageInput').val()
                },
                success: function(res) {
                    $('#messageInput').val('');
                }
            });
        });

        // Setup Echo
        Pusher.logToConsole = false;

        window.Echo = new Echo({
            broadcaster: 'pusher',
            key: '<?php echo e(env('PUSHER_APP_KEY')); ?>',
            cluster: 'mt1',
            forceTLS: true
        });

        Echo.channel('chat-channel')
            .listen('.message.sent', (e) => {
                toastr.success('New message: ' + e.message);
            });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\INCT\Parth\practice projects\laravel-pusher\resources\views/chat.blade.php ENDPATH**/ ?>