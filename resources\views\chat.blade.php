@extends('layouts.app')

@section('content')
    <h3>Send Message</h3>

    <form id="messageForm">
        @csrf
        <div class="mb-3">
            <input type="text" class="form-control" name="message" id="messageInput" placeholder="Enter message" required>
        </div>
        <button type="submit" class="btn btn-primary">Send</button>
    </form>
@endsection

@push('scripts')
    <script>
        $('#messageForm').submit(function(e) {
            e.preventDefault();

            $.ajax({
                url: '/send-message',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    message: $('#messageInput').val()
                },
                success: function(res) {
                    $('#messageInput').val('');
                }
            });
        });

        // Setup Echo
        Pusher.logToConsole = false;

        window.Echo = new Echo({
            broadcaster: 'pusher',
            key: '{{ env('PUSHER_APP_KEY') }}',
            cluster: 'mt1',
            forceTLS: true
        });

        Echo.channel('chat-channel')
            .listen('.message.sent', (e) => {
                toastr.success('New message: ' + e.message);
            });
    </script>
@endpush
