<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class NewMessageNotification implements ShouldBroadcast
{
    public $message;
    public $fromUser;
    public $toUser;

    public function __construct($message, $fromUser, $toUser)
    {
        $this->message = $message;
        $this->fromUser = $fromUser;
        $this->toUser = $toUser;
    }

    public function broadcastOn()
    {
        return new Channel('user.' . $this->toUser->id);
    }

    public function broadcastAs()
    {
        return 'new-message';
    }
}
